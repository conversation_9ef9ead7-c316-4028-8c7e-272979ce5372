import React from 'react';
import { Sender, Attachments, Prompts } from '@ant-design/x';
import MessageList from './MessageList';
import { AgentParam } from '../../../types/agentParam';

interface ChatInterfaceProps {
  parsedMessages: any[];
  currentAgentParam: AgentParam | null;
  messageFeedBackDict: { [key: string]: string | null };
  handleFeedback: (messageId: string, rating: string | null) => void;
  handleSuggestionPromptsClick: (prompt: string) => void;
  loadConversation: (activeKey: string, agentId: string) => Promise<any>;
  activeKey: string;
  currentAgent: any;
  realtimePlayEnabled: boolean;
  onSubmit: (message: string) => void;
  onCancel: () => void;
  content: string;
  setContent: (content: string) => void;
  attachedFiles: any[];
  setAttachedFiles: (files: any[]) => void;
  suggestedPrompts: any[];
  currentTaskId: string | null;
  isSpeechRecording: boolean;
  setIsSpeechRecording: (recording: boolean) => void;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  parsedMessages,
  currentAgentParam,
  messageFeedBackDict,
  handleFeedback,
  handleSuggestionPromptsClick,
  loadConversation,
  activeKey,
  currentAgent,
  realtimePlayEnabled,
  onSubmit,
  onCancel,
  content,
  setContent,
  attachedFiles,
  setAttachedFiles,
  suggestedPrompts,
  currentTaskId,
  isSpeechRecording,
  setIsSpeechRecording
}) => {
  
  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 消息列表区域 */}
      <div style={{ flex: 1, overflow: 'hidden' }}>
        <MessageList
          parsedMessages={parsedMessages}
          currentAgentParam={currentAgentParam}
          messageFeedBackDict={messageFeedBackDict}
          handleFeedback={handleFeedback}
          handleSuggestionPromptsClick={handleSuggestionPromptsClick}
          loadConversation={loadConversation}
          activeKey={activeKey}
          currentAgent={currentAgent}
          realtimePlayEnabled={realtimePlayEnabled}
          style={{ height: '100%' }}
        />
      </div>

      {/* 建议问题区域 */}
      {suggestedPrompts && suggestedPrompts.length > 0 && (
        <div style={{ padding: '16px', borderTop: '1px solid #f0f0f0' }}>
          <Prompts
            title="建议问题"
            items={suggestedPrompts}
            onItemClick={(info) => {
              handleSuggestionPromptsClick(info.data.description);
            }}
            style={{ marginBottom: 16 }}
          />
        </div>
      )}

      {/* 输入区域 */}
      <div style={{ padding: '16px', borderTop: '1px solid #f0f0f0' }}>
        {/* 附件上传区域 */}
        {attachedFiles && attachedFiles.length > 0 && (
          <div style={{ marginBottom: 16 }}>
            <Attachments
              items={attachedFiles}
              onChange={setAttachedFiles}
            />
          </div>
        )}

        {/* 发送器 */}
        <Sender
          value={content}
          onChange={setContent}
          onSubmit={onSubmit}
          onCancel={onCancel}
          placeholder="输入消息..."
          loading={!!currentTaskId}
          disabled={isSpeechRecording}
          style={{ width: '100%' }}
        />
      </div>
    </div>
  );
};

export default ChatInterface;
