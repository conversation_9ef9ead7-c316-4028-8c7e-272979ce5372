import { useCallback } from 'react';
import { message as antdMessage } from 'antd';
import { AgentDict } from '../../../types/agentDict';
import { UserAgentList } from '../../../types/userAgentList';
import { MessageInfo } from '@ant-design/x/es/use-x-chat';
import { AgentMessage } from '../../../types/streamResponse';
import { feedback } from '../../../services/difyApi';
import { deleteConversation, renameConversation, topConversation } from '../../../services/chatHistory';
import { deleteUserAgent, createUserAgent } from '../../../services/userAgentList';

export interface EventHandlers {
  // 对话相关事件
  handleConversationClick: (conversationId: string, agentDict: AgentDict) => void;
  handleConversationMenuClick: (conversationId: string, menuKey: string, conversationName: string, isTop: boolean) => void;
  handleConfirmRename: () => Promise<void>;
  handleCancelRename: () => void;
  
  // Agent相关事件
  handleAgentClick: (agent: AgentDict) => void;
  handleAgentManagerClick: () => void;
  handleAgentFormSubmit: (values: any) => void;
  handleAgentFormCancel: () => void;
  handleAgentVisibleClick: (agent: AgentDict) => void;
  handleVisibleSelectorConfirm: () => Promise<void>;
  handleVisibleSelectorCancel: () => void;
  
  // 消息相关事件
  handleFeedback: (messageId: string, rating: string | null) => Promise<void>;
  handleSuggestionPromptsClick: (prompt: string) => void;
  
  // 表单相关事件
  onSubmit: (message: string) => void;
  onCancel: () => void;
  
  // UI相关事件
  handleMenuToggle: () => void;
  handleDrawerToggle: () => void;
  handleHeaderToggle: () => void;
}

interface UseEventHandlersProps {
  // 状态
  currentAgent: AgentDict | null;
  defaultAgent: AgentDict | null;
  beRenameConversionId: string | null;
  renameModalType: 'rename' | 'topChat';
  visibleSelectAgentDictId: string;
  content: string;
  collapsed: boolean;
  drawerOpen: boolean;
  headerOpen: boolean;
  
  // 状态设置函数
  setCurrentAgent: (agent: AgentDict | null) => void;
  setCurrentAgentParam: (param: any) => void;
  setContentShowState: (state: any) => void;
  setCurrentSuperSonicAgentId: (id: string | null) => void;
  setSelectedAgentId: (id: string) => void;
  setIsShowRenameModal: (show: boolean) => void;
  setBeRenameConversionId: (id: string | null) => void;
  setIsOpenAgentFormModal: (open: boolean) => void;
  setAgentFormData: (data: AgentDict | null) => void;
  setIsShowVisibleSelectorDialog: (show: boolean) => void;
  setVisibleSelectAgentDictId: (id: string) => void;
  setContent: (content: string) => void;
  setCollapsed: (collapsed: boolean) => void;
  setDrawerOpen: (open: boolean) => void;
  setHeaderOpen: (open: boolean) => void;
  setIsBeRefreshConversations: (refresh: boolean) => void;
  setBeLoadHistoryMessageConversationId: (id: string) => void;
  setMessages: (messages: MessageInfo<AgentMessage>[]) => void;
  setMessageFeedBackDict: (dict: any) => void;
  
  // 业务函数
  loadAgentParameters: (agent: AgentDict) => Promise<any>;
  initializeMessage: (agentParam: any, setMessages: any, currentAgent: any, defaultAgent: any) => void;
  fetchAgentList: () => Promise<void>;
  onRequest: (message: any) => void;
  chatNameForm: any;
}

export const useEventHandlers = (props: UseEventHandlersProps): EventHandlers => {
  const {
    currentAgent, defaultAgent, beRenameConversionId, renameModalType,
    visibleSelectAgentDictId, content, collapsed, drawerOpen, headerOpen,
    setCurrentAgent, setCurrentAgentParam, setContentShowState, setCurrentSuperSonicAgentId,
    setSelectedAgentId, setIsShowRenameModal, setBeRenameConversionId,
    setIsOpenAgentFormModal, setAgentFormData, setIsShowVisibleSelectorDialog,
    setVisibleSelectAgentDictId, setContent, setCollapsed, setDrawerOpen,
    setHeaderOpen, setIsBeRefreshConversations, setBeLoadHistoryMessageConversationId,
    setMessages, setMessageFeedBackDict, loadAgentParameters, initializeMessage,
    fetchAgentList, onRequest, chatNameForm
  } = props;

  // 对话相关事件处理
  const handleConversationClick = useCallback((conversationId: string, agentDict: AgentDict) => {
    if (currentAgent && currentAgent.id !== agentDict.id) {
      setCurrentAgent(agentDict);
      loadAgentParameters(agentDict).then((agentParam) => {
        if (agentParam) {
          initializeMessage(agentParam, setMessages, agentDict, defaultAgent);
        }
        setBeLoadHistoryMessageConversationId(conversationId);
      });
    } else {
      setBeLoadHistoryMessageConversationId(conversationId);
    }
  }, [currentAgent, defaultAgent, setCurrentAgent, loadAgentParameters, initializeMessage, setMessages, setBeLoadHistoryMessageConversationId]);

  const handleConversationMenuClick = useCallback((conversationId: string, menuKey: string, conversationName: string, isTop: boolean) => {
    if (menuKey === 'rename') {
      setBeRenameConversionId(conversationId);
      setIsShowRenameModal(true);
      chatNameForm.setFieldsValue({ name: conversationName });
    } else if (menuKey === 'top') {
      handleTopConversation(conversationId, !isTop);
    } else if (menuKey === 'delete') {
      handleDeleteConversation(conversationId);
    }
  }, [setBeRenameConversionId, setIsShowRenameModal, chatNameForm]);

  const handleTopConversation = async (conversationId: string, isTop: boolean) => {
    try {
      await topConversation(conversationId, isTop);
      setIsBeRefreshConversations(true);
      antdMessage.success(isTop ? '已固定' : '已取消固定');
    } catch (error) {
      antdMessage.error('操作失败');
    }
  };

  const handleDeleteConversation = async (conversationId: string) => {
    try {
      await deleteConversation(conversationId);
      setIsBeRefreshConversations(true);
      antdMessage.success('删除成功');
    } catch (error) {
      antdMessage.error('删除失败');
    }
  };

  const handleConfirmRename = useCallback(async () => {
    if (!beRenameConversionId) return;
    
    try {
      const values = await chatNameForm.validateFields();
      await renameConversation(beRenameConversionId, values.name);
      setIsShowRenameModal(false);
      setBeRenameConversionId(null);
      setIsBeRefreshConversations(true);
      antdMessage.success('重命名成功');
    } catch (error) {
      antdMessage.error('重命名失败');
    }
  }, [beRenameConversionId, chatNameForm, setIsShowRenameModal, setBeRenameConversionId, setIsBeRefreshConversations]);

  const handleCancelRename = useCallback(() => {
    setIsShowRenameModal(false);
    setBeRenameConversionId(null);
    chatNameForm.resetFields();
  }, [setIsShowRenameModal, setBeRenameConversionId, chatNameForm]);

  // Agent相关事件处理
  const handleAgentClick = useCallback((agent: AgentDict) => {
    if (agent.id.startsWith('supersonic-')) {
      setContentShowState('supersonic');
      setCurrentSuperSonicAgentId(agent.id);
    } else {
      setContentShowState('agent');
      setSelectedAgentId(agent.id);
      setCurrentAgent(agent);
      loadAgentParameters(agent).then((agentParam) => {
        if (agentParam) {
          initializeMessage(agentParam, setMessages, agent, defaultAgent);
        }
      });
    }
  }, [setContentShowState, setCurrentSuperSonicAgentId, setSelectedAgentId, setCurrentAgent, loadAgentParameters, initializeMessage, setMessages, defaultAgent]);

  const handleAgentManagerClick = useCallback(() => {
    setContentShowState('agentManager');
  }, [setContentShowState]);

  const handleAgentFormSubmit = useCallback((values: any) => {
    // Agent表单提交逻辑
    setIsOpenAgentFormModal(false);
    setAgentFormData(null);
    fetchAgentList();
  }, [setIsOpenAgentFormModal, setAgentFormData, fetchAgentList]);

  const handleAgentFormCancel = useCallback(() => {
    setIsOpenAgentFormModal(false);
    setAgentFormData(null);
  }, [setIsOpenAgentFormModal, setAgentFormData]);

  const handleAgentVisibleClick = useCallback((agent: AgentDict) => {
    setVisibleSelectAgentDictId(agent.id);
    setIsShowVisibleSelectorDialog(true);
  }, [setVisibleSelectAgentDictId, setIsShowVisibleSelectorDialog]);

  const handleVisibleSelectorConfirm = useCallback(async () => {
    try {
      await createUserAgent(visibleSelectAgentDictId);
      setIsShowVisibleSelectorDialog(false);
      setVisibleSelectAgentDictId('');
      fetchAgentList();
      antdMessage.success('添加成功');
    } catch (error) {
      antdMessage.error('添加失败');
    }
  }, [visibleSelectAgentDictId, setIsShowVisibleSelectorDialog, setVisibleSelectAgentDictId, fetchAgentList]);

  const handleVisibleSelectorCancel = useCallback(() => {
    setIsShowVisibleSelectorDialog(false);
    setVisibleSelectAgentDictId('');
  }, [setIsShowVisibleSelectorDialog, setVisibleSelectAgentDictId]);

  // 消息相关事件处理
  const handleFeedback = useCallback(async (messageId: string, rating: string | null) => {
    try {
      await feedback(messageId, rating);
      setMessageFeedBackDict((prev: any) => ({
        ...prev,
        [messageId]: rating
      }));
      antdMessage.success(rating ? '反馈成功' : '已取消反馈');
    } catch (error) {
      antdMessage.error('反馈失败');
    }
  }, [setMessageFeedBackDict]);

  const handleSuggestionPromptsClick = useCallback((prompt: string) => {
    setContent(prompt);
    onSubmit(prompt);
  }, [setContent, onRequest]);

  // 表单相关事件处理
  const onSubmit = useCallback((message: string) => {
    if (!message.trim()) return;
    
    const requestMessage = {
      content: message,
      agentId: currentAgent?.id,
      conversationId: undefined, // 根据需要设置
      fileList: [], // 根据需要设置
      inputs: {}, // 根据需要设置
      parentMessageId: undefined,
      suggested: false,
      isAudioPlayer: false // 根据需要设置
    };
    
    onRequest(requestMessage);
    setContent('');
  }, [currentAgent, onRequest, setContent]);

  const onCancel = useCallback(() => {
    setContent('');
  }, [setContent]);

  // UI相关事件处理
  const handleMenuToggle = useCallback(() => {
    setCollapsed(!collapsed);
  }, [collapsed, setCollapsed]);

  const handleDrawerToggle = useCallback(() => {
    setDrawerOpen(!drawerOpen);
  }, [drawerOpen, setDrawerOpen]);

  const handleHeaderToggle = useCallback(() => {
    setHeaderOpen(!headerOpen);
  }, [headerOpen, setHeaderOpen]);

  return {
    handleConversationClick,
    handleConversationMenuClick,
    handleConfirmRename,
    handleCancelRename,
    handleAgentClick,
    handleAgentManagerClick,
    handleAgentFormSubmit,
    handleAgentFormCancel,
    handleAgentVisibleClick,
    handleVisibleSelectorConfirm,
    handleVisibleSelectorCancel,
    handleFeedback,
    handleSuggestionPromptsClick,
    onSubmit,
    onCancel,
    handleMenuToggle,
    handleDrawerToggle,
    handleHeaderToggle,
  };
};
