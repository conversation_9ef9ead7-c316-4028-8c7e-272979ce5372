import React from 'react';
import { AgentParam } from '../../../types/agentParam';
import { FileUploadButton, DeepThinkButton, RealtimePlayButton } from './buttons';

interface SenderFooterProps {
  currentAgentParam: AgentParam | null;
  headerOpen: boolean;
  attachedFilesCount: number;
  defaultAgentIsOpenThink: boolean;
  isDisableSwitchOpenThink: boolean;
  hasMessages: boolean;
  realtimePlayEnabled: boolean;
  onFileUploadToggle: () => void;
  onDeepThinkToggle: () => void;
  onRealtimePlayToggle: () => void;
}

const SenderFooter: React.FC<SenderFooterProps> = ({
  currentAgentParam,
  headerOpen,
  attachedFilesCount,
  defaultAgentIsOpenThink,
  isDisableSwitchOpenThink,
  hasMessages,
  realtimePlayEnabled,
  onFileUploadToggle,
  onDeepThinkToggle,
  onRealtimePlayToggle
}) => {
  return (
    <div
      style={{ padding: '8px 0', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
    >
      <div className="flex items-center flex-wrap">
        {/* 上传参考文件 */}
        {currentAgentParam?.file_upload?.enabled && (
          <FileUploadButton
            headerOpen={headerOpen}
            attachedFilesCount={attachedFilesCount}
            onToggle={onFileUploadToggle}
          />
        )}
        
        {/* 深度思考 */}
        <DeepThinkButton
          defaultAgentIsOpenThink={defaultAgentIsOpenThink}
          isDisableSwitchOpenThink={isDisableSwitchOpenThink}
          hasMessages={hasMessages}
          onToggle={onDeepThinkToggle}
        />

        {/* 实时播放按钮 */}
        <RealtimePlayButton
          realtimePlayEnabled={realtimePlayEnabled}
          onToggle={onRealtimePlayToggle}
        />
      </div>
    </div>
  );
};

export default SenderFooter;
