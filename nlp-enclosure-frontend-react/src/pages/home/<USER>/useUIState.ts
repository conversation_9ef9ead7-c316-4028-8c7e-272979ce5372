import { useState, useEffect } from 'react';

export type ContentShowState = 'defaultChat' | 'agent' | 'agentManager' | 'supersonic';

export interface UIState {
  // Loading状态
  isShowSpin: boolean;
  setIsShowSpin: (value: boolean) => void;
  
  // 头部状态
  headerOpen: boolean;
  setHeaderOpen: (value: boolean) => void;
  
  // 侧边栏状态
  collapsed: boolean;
  setCollapsed: (value: boolean) => void;
  
  // 移动端抽屉状态
  drawerOpen: boolean;
  setDrawerOpen: (value: boolean) => void;
  
  // Logo显示状态
  isLogoSpanVisible: boolean;
  setIsLogoSpanVisible: (value: boolean) => void;
  
  // 内容显示状态
  contentShowState: ContentShowState;
  setContentShowState: (value: ContentShowState) => void;
  
  // 激活的对话Key
  activeKey: string;
  setActiveKey: (value: string) => void;
  
  // 菜单选中Key
  nodeMenuSelectedKey: string;
  setNodeMenuSelectedKey: (value: string) => void;
  
  // 选中的Agent ID
  selectedAgentId: string;
  setSelectedAgentId: (value: string) => void;
}

export const useUIState = (): UIState => {
  // Loading状态
  const [isShowSpin, setIsShowSpin] = useState(false);
  
  // 头部状态
  const [headerOpen, setHeaderOpen] = useState(false);
  
  // 侧边栏状态
  const [collapsed, setCollapsed] = useState(false);
  
  // 移动端抽屉状态
  const [drawerOpen, setDrawerOpen] = useState(false);
  
  // Logo显示状态
  const [isLogoSpanVisible, setIsLogoSpanVisible] = useState(!collapsed || drawerOpen);
  
  // 内容显示状态
  const [contentShowState, setContentShowState] = useState<ContentShowState>('defaultChat');
  
  // 激活的对话Key
  const [activeKey, setActiveKey] = useState<string>('auto');
  
  // 菜单选中Key
  const [nodeMenuSelectedKey, setNodeMenuSelectedKey] = useState<string>('');
  
  // 选中的Agent ID
  const [selectedAgentId, setSelectedAgentId] = useState<string>('');

  // Logo显示逻辑 - 延迟显示
  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;
    if (!collapsed || drawerOpen) {
      timer = setTimeout(() => {
        setIsLogoSpanVisible(true);
      }, 200); // 延迟200毫秒
    } else {
      setIsLogoSpanVisible(false);
    }

    // 处理对话图标显示/隐藏
    const display = collapsed ? 'none' : 'block';
    const element = document.getElementsByClassName("ant-conversations-icon");
    if (element.length > 0) {
      for (let i = 0; i < element.length; i++) {
        element[i].style.display = display;
      }
    }

    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [collapsed, drawerOpen]);

  return {
    isShowSpin,
    setIsShowSpin,
    headerOpen,
    setHeaderOpen,
    collapsed,
    setCollapsed,
    drawerOpen,
    setDrawerOpen,
    isLogoSpanVisible,
    setIsLogoSpanVisible,
    contentShowState,
    setContentShowState,
    activeKey,
    setActiveKey,
    nodeMenuSelectedKey,
    setNodeMenuSelectedKey,
    selectedAgentId,
    setSelectedAgentId,
  };
};
