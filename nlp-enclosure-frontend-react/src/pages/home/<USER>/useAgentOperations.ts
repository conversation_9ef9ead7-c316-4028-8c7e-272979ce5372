import { useState } from 'react';
import { message as antdMessage } from 'antd';
import { AgentDict, getColorPairByBgColor } from '../../../types/agentDict';
import { AgentParam } from '../../../types/agentParam';
import { UserAgentList } from '../../../types/userAgentList';
import { MessageInfo } from '@ant-design/x/es/use-x-chat';
import { AgentMessage } from '../../../types/streamResponse';
import { parameters } from '../../../services/difyApi';
import { findAllVisible, getAgentDictByIsDefaultTrue } from '../../../services/agentDict';
import { getUserAgentList } from '../../../services/userAgentList';
import { isSuperSonicAgent, createSuperSonicAgentParam } from '../../../services/supersonic';

export interface AgentOperations {
  // Agent相关状态
  agentList: AgentDict[];
  setAgentList: (agents: AgentDict[]) => void;
  
  currentUserAgentList: UserAgentList[];
  setCurrentUserAgentList: (list: UserAgentList[]) => void;
  
  currentAgent: AgentDict | null;
  setCurrentAgent: (agent: AgentDict | null) => void;
  
  currentAgentParam: AgentParam | null;
  setCurrentAgentParam: (param: AgentParam | null) => void;
  
  defaultAgent: AgentDict | null;
  setDefaultAgent: (agent: AgentDict | null) => void;
  
  defaultAgentIsOpenThink: boolean;
  setDefaultAgentIsOpenThink: (value: boolean) => void;
  
  isDisableSwitchOpenThink: boolean;
  setIsDisableSwitchOpenThink: (value: boolean) => void;
  
  currentTaskId: string | null;
  setCurrentTaskId: (id: string | null) => void;
  
  currentSuperSonicAgentId: string | null;
  setCurrentSuperSonicAgentId: (id: string | null) => void;
  
  isCanCreateAgent: boolean;
  setIsCanCreateAgent: (value: boolean) => void;
  
  agentFormData: AgentDict | null;
  setAgentFormData: (data: AgentDict | null) => void;
  
  suggestedPrompts: any[];
  setSuggestedPrompts: (prompts: any[]) => void;
  
  // 业务方法
  loadAgentParameters: (agent: AgentDict) => Promise<AgentParam | null>;
  initializeMessage: (agentParam: AgentParam, setMessages: (messages: MessageInfo<AgentMessage>[]) => void, currentAgent: AgentDict | null, defaultAgent: AgentDict | null) => void;
  fetchAgentList: () => Promise<void>;
  fetchDefaultAgent: (llmDefaultSelect: string, handleUpdateOpenThinkSwitch: (agent: AgentDict | null, llmName: string) => void) => Promise<void>;
  updateDefaultAgentStateByMessage: (message: any, currentAgent: AgentDict | null, defaultAgent: AgentDict | null, setDefaultAgentIsOpenThink: (value: boolean) => void, setLlmDefaultSelect: (value: string) => void) => void;
}

export const useAgentOperations = (): AgentOperations => {
  // Agent相关状态
  const [agentList, setAgentList] = useState<AgentDict[]>([]);
  const [currentUserAgentList, setCurrentUserAgentList] = useState<UserAgentList[]>([]);
  const [currentAgent, setCurrentAgent] = useState<AgentDict | null>(null);
  const [currentAgentParam, setCurrentAgentParam] = useState<AgentParam | null>(null);
  const [defaultAgent, setDefaultAgent] = useState<AgentDict | null>(null);
  const [defaultAgentIsOpenThink, setDefaultAgentIsOpenThink] = useState(false);
  const [isDisableSwitchOpenThink, setIsDisableSwitchOpenThink] = useState(false);
  const [currentTaskId, setCurrentTaskId] = useState<string | null>(null);
  const [currentSuperSonicAgentId, setCurrentSuperSonicAgentId] = useState<string | null>(null);
  const [isCanCreateAgent, setIsCanCreateAgent] = useState(false);
  const [agentFormData, setAgentFormData] = useState<AgentDict | null>(null);
  const [suggestedPrompts, setSuggestedPrompts] = useState<any[]>([]);

  // 加载Agent参数
  const loadAgentParameters = async (agent: AgentDict): Promise<AgentParam | null> => {
    if (!agent) {
      return null;
    }

    // 如果是 SuperSonic 类型的 agent，使用内置参数
    if (isSuperSonicAgent(agent)) {
      const superSonicParam = createSuperSonicAgentParam();
      setCurrentAgentParam(superSonicParam);

      // 设置建议问题
      setSuggestedPrompts(superSonicParam.suggested_questions.map((question, index) => ({
        key: `supersonic-${index}`,
        description: question
      })));

      return superSonicParam;
    }

    // 其他类型的 agent 从服务器加载参数
    try {
      const agentParam = await parameters(agent.id);
      setCurrentAgentParam(agentParam);
      return agentParam;
    } catch (e) {
      console.error('Error loading agent parameters:', e);
      antdMessage.error('加载参数失败');
      return null;
    }
  };

  // 初始化消息
  const initializeMessage = (
    agentParam: AgentParam, 
    setMessages: (messages: MessageInfo<AgentMessage>[]) => void,
    currentAgent: AgentDict | null,
    defaultAgent: AgentDict | null
  ): void => {
    if (!agentParam) {
      setMessages([]);
      return;
    }

    // 判断如果是默认agent，直接跳过
    if (currentAgent && currentAgent.id === defaultAgent?.id) {
      setMessages([]);
      return;
    }

    if (agentParam.opening_statement) {
      setMessages([
        {
          id: 'welcome',
          message: {
            type: 'ai',
            content: agentParam.opening_statement,
            suggested: false
          },
          status: 'success'
        }
      ]);
    } else {
      setMessages([]);
    }
  };

  // 获取Agent列表
  const fetchAgentList = async (): Promise<void> => {
    const [userAgentListResult, agentListResult] = await Promise.all([getUserAgentList(), findAllVisible()]);

    const userAgentList = userAgentListResult;
    const agentList = agentListResult;

    if (userAgentList && userAgentList.length > 0) {
      userAgentList.forEach((userAgent: UserAgentList) => {
        if (userAgent.agentDict) {
          userAgent.agentDict.iconColor = userAgent.agentDict.iconColor || '#dfdff8';
          userAgent.agentDict.fontColor = getColorPairByBgColor(userAgent.agentDict.iconColor).textColor;
        }
      });
      setCurrentUserAgentList(userAgentList);
    } else {
      setCurrentUserAgentList([]);
    }

    if (agentList && agentList.payload) {
      // 填充颜色代码
      agentList.payload.forEach((agent: AgentDict) => {
        agent.iconColor = agent.iconColor || '#dfdff8';
        agent.fontColor = getColorPairByBgColor(agent.iconColor).textColor;

        // 检查 agent 的 id 是否存在于 userAgentList 中
        const userAgent = userAgentList.find((ua: UserAgentList) => ua.agentDictId === agent.id);
        if (userAgent) {
          agent.userAgentId = userAgent.id; // 如果存在，赋值 userAgentId
        }
      });
      setAgentList(agentList.payload);
    }
  };

  // 获取默认Agent
  const fetchDefaultAgent = async (
    llmDefaultSelect: string, 
    handleUpdateOpenThinkSwitch: (agent: AgentDict | null, llmName: string) => void
  ): Promise<void> => {
    const defaultAgent = await getAgentDictByIsDefaultTrue();
    if (defaultAgent && defaultAgent.payload) {
      setDefaultAgent(defaultAgent.payload);
      setCurrentAgent(defaultAgent.payload);
      handleUpdateOpenThinkSwitch(defaultAgent.payload, llmDefaultSelect);
    }
  };

  // 根据聊天历史修正默认agent的配置
  const updateDefaultAgentStateByMessage = (
    message: any,
    currentAgent: AgentDict | null,
    defaultAgent: AgentDict | null,
    setDefaultAgentIsOpenThink: (value: boolean) => void,
    setLlmDefaultSelect: (value: string) => void
  ): void => {
    if (!message) {
      return;
    }

    // 判断是不是默认Agent
    if (currentAgent && currentAgent.id !== defaultAgent?.id) {
      return;
    }

    const input = message.inputs;
    if (input) {
      if (input.llm === 'QwQ:32B') {
        setDefaultAgentIsOpenThink(true);
        setLlmDefaultSelect('Qwen2.5:32B-instruct');
      } else if (input.llm === 'DeepSeek-R1:671B') {
        setDefaultAgentIsOpenThink(true);
        setLlmDefaultSelect('DeepSeek-R1:671B');
      } else if (input.llm === 'Qwen2.5-VL:32B-instruct') {
        setDefaultAgentIsOpenThink(false);
        setLlmDefaultSelect('Qwen2.5-VL:32B-instruct');
      } else {
        setDefaultAgentIsOpenThink(false);
        setLlmDefaultSelect('Qwen2.5:32B-instruct');
      }
    }
  };

  return {
    agentList,
    setAgentList,
    currentUserAgentList,
    setCurrentUserAgentList,
    currentAgent,
    setCurrentAgent,
    currentAgentParam,
    setCurrentAgentParam,
    defaultAgent,
    setDefaultAgent,
    defaultAgentIsOpenThink,
    setDefaultAgentIsOpenThink,
    isDisableSwitchOpenThink,
    setIsDisableSwitchOpenThink,
    currentTaskId,
    setCurrentTaskId,
    currentSuperSonicAgentId,
    setCurrentSuperSonicAgentId,
    isCanCreateAgent,
    setIsCanCreateAgent,
    agentFormData,
    setAgentFormData,
    suggestedPrompts,
    setSuggestedPrompts,
    loadAgentParameters,
    initializeMessage,
    fetchAgentList,
    fetchDefaultAgent,
    updateDefaultAgentStateByMessage,
  };
};
