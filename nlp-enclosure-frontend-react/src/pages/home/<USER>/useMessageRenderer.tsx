import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>g<PERSON><PERSON><PERSON>, Popover, Image } from 'antd';
import { GetProp } from 'antd';
import { Bubble, BubbleProps, Attachments } from '@ant-design/x';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import { CopyOutlined, LikeOutlined, LikeFilled, DislikeOutlined, DislikeFilled } from '@ant-design/icons';
import { message as antdMessage } from 'antd';
import { md } from '../../../utils/markdown';
import { RetrieverResource } from '../../../types/streamResponse';
import { AgentParam } from '../../../types/agentParam';
import { MessageInfo } from '@ant-design/x/es/use-x-chat';
import { AgentMessage } from '../../../types/streamResponse';
import PlayButton from '../components/PlayButton';

// 按钮主题配置
const buttonTheme = {
  components: {
    Button: {
      colorPrimary: '#000',
      algorithm: true,
    },
  },
};

export interface MessageRendererHook {
  renderMarkdown: BubbleProps['messageRender'];
  retrieverPopover: (groupedData: Record<string, RetrieverResource[]>) => React.ReactNode[];
  retrieverPopoverContent: (items: RetrieverResource[]) => React.ReactNode;
  renderMessageItems: (
    parsedMessages: any[],
    currentAgentParam: AgentParam | null,
    messageFeedBackDict: { [key: string]: string | null },
    handleFeedback: (messageId: string, rating: string | null) => void,
    loadConversation: (activeKey: string, agentId: string) => Promise<any>,
    activeKey: string,
    currentAgent: any,
    realtimePlayEnabled: boolean
  ) => GetProp<typeof Bubble.List, 'items'>;
}

export const useMessageRenderer = (): MessageRendererHook => {
  
  const renderMarkdown: BubbleProps['messageRender'] = (content) => {
    return (
      <Typography>
        <div className="ai-content-class" dangerouslySetInnerHTML={{ __html: md.render(content) }} />
      </Typography>
    );
  };

  const retrieverPopoverContent = (items: RetrieverResource[]) => {
    return (
      <div
        style={{
          width: '500px',
          height: '300px',
          overflow: 'auto',
          paddingRight: 10,
          scrollbarColor: '#888 transparent',
          scrollbarWidth: 'thin'
        }}
      >
        {items.map((item, index) => (
          <div
            key={index}
            style={{
              padding: '10px',
              border: '1px solid #e8e8e8',
              borderRadius: '4px',
              marginTop: '10px',
              marginBottom: '10px'
            }}
          >
            <div className="flex w-12 items-center px-1.5 h-5 border border-gray-200 rounded-md mb-1">
              <span className="text-[11px] font-medium text-gray-500">
                # {item.segment_position}
              </span>
            </div>
            <Typography.Paragraph>{item.content}</Typography.Paragraph>
          </div>
        ))}
      </div>
    );
  };

  const retrieverPopover = (groupedData: Record<string, RetrieverResource[]>) => {
    return Object.entries(groupedData).map(([documentId, items]) => {
      const documentName = items[0]?.document_name || '无标题';

      return (
        <ConfigProvider key={documentId} theme={buttonTheme}>
          <Popover
            title={documentName}
            trigger="click"
            content={retrieverPopoverContent(items)}
          >
            <Tooltip title={documentName}>
              <Button className="m-2 w-40 overflow-hidden">
                <span className="w-full text-xs truncate">{documentName}</span>
              </Button>
            </Tooltip>
          </Popover>
        </ConfigProvider>
      );
    });
  };

  const renderMessageItems = (
    parsedMessages: any[],
    currentAgentParam: AgentParam | null,
    messageFeedBackDict: { [key: string]: string | null },
    handleFeedback: (messageId: string, rating: string | null) => void,
    loadConversation: (activeKey: string, agentId: string) => Promise<any>,
    activeKey: string,
    currentAgent: any,
    realtimePlayEnabled: boolean
  ): GetProp<typeof Bubble.List, 'items'> => {
    
    return parsedMessages.map(({ id, message, status }) => {
      let content;
      let loading = false;
      let isShowFooter = true;
      let lId = id;

      if (message.id) {
        lId = message.id;
      }

      // 根据消息类型和状态确定内容和loading状态
      if (message.type === 'ai') {
        if (message.content && typeof message.content === 'string' && message.content.length > 0) {
          content = renderMarkdown(message.content);
        } else if (status === 'success') {
          content = '';
          isShowFooter = false;
        } else {
          loading = true;
        }

        // 处理检索资源
        if (
          status === 'success' &&
          currentAgentParam?.retriever_resource?.enabled &&
          message.retrieverResources &&
          message.retrieverResources.length > 0
        ) {
          loading = false;
          const grouped: Record<string, RetrieverResource[]> = {};
          message.retrieverResources.forEach((retriever) => {
            const key = retriever.document_id;
            if (!grouped[key]) {
              grouped[key] = [];
            }
            grouped[key].push(retriever);
          });

          content = (
            <>
              {renderMarkdown(message.content)}
              <div>
                <Divider
                  style={{ margin: '5px 0', borderColor: 'rgba(0,0,0,.25)' }}
                  orientation="left"
                >
                  引用
                </Divider>
                <div>{retrieverPopover(grouped)}</div>
              </div>
            </>
          );
        }

      } else if (message.type === 'local') {
        if (message.content && typeof message.content === 'string' && message.content.length > 0) {
          if (message.fileList && message.fileList.length > 0) {
            content = (
              <>
                {message.fileList.map((file: any, fileIndex: number) => {
                  if (file.type === 'document') {
                    return <Attachments.FileCard key={file.file.uid || fileIndex} item={file.file} />;
                  } else {
                    return (
                      <Image.PreviewGroup key={fileIndex}>
                        <Image
                          width={100}
                          src={file.url ? file.url : (file.file.originFileObj ? URL.createObjectURL(file.file.originFileObj) : '')}
                          style={{ marginRight: '10px' }}
                        />
                      </Image.PreviewGroup>
                    );
                  }
                })}
                {renderMarkdown(message.content)}
              </>
            );
          } else {
            content = renderMarkdown(message.content);
          }
        } else if (loading) {
          content = '发送中...';
          isShowFooter = false;
        } else {
          content = '';
        }

      } else if (message.type === 'suggestion') {
        content = message.content;
        loading = false;
        isShowFooter = false;
      } else {
        content = message.content;
        loading = false;
        isShowFooter = false;
      }

      // 确定页脚内容
      let footer = null;
      if (!loading && isShowFooter && status === 'success' && message.type === 'ai') {
        footer = (
          <div style={{ display: 'flex', justifyContent: 'space-between', width: '96%', marginLeft: 10 }}>
            <div style={{ display: 'flex' }}>
              <Tooltip title="复制">
                <CopyToClipboard 
                  text={typeof message.content === 'string' ? message.content : ''} 
                  onCopy={() => antdMessage.success('内容已复制到剪贴板')}
                >
                  <Button color="default" variant="text" size="small" icon={<CopyOutlined />} />
                </CopyToClipboard>
              </Tooltip>
              {!realtimePlayEnabled && (
                <PlayButton
                  message={typeof message.content === 'string' ? message.content : ''}
                  disabled={!message.content}
                />
              )}
            </div>
            <div className="flex items-center">
              <Tooltip title={messageFeedBackDict[lId] === 'like' ? '取消点赞' : '点赞'}>
                <Button
                  color="default"
                  variant="text"
                  size="small"
                  icon={messageFeedBackDict[lId] === 'like' ? <LikeFilled style={{color: '#1890ff'}} /> : <LikeOutlined />}
                  onClick={() => handleFeedback(lId, messageFeedBackDict[lId] === 'like' ? null : 'like')}
                  style={{ marginRight: 10}}
                />
              </Tooltip>
              <Tooltip title={messageFeedBackDict[lId] === 'dislike' ? '取消点踩' : '点踩'}>
                <Button
                  color="default"
                  variant="text"
                  size="small"
                  icon={messageFeedBackDict[lId] === 'dislike' ? <DislikeFilled style={{color: '#dc2626'}} /> : <DislikeOutlined />}
                  onClick={() => handleFeedback(lId, messageFeedBackDict[lId] === 'dislike' ? null : 'dislike')}
                />
              </Tooltip>
            </div>
          </div>
        );
      }

      // 在AI消息成功后延迟加载对话
      if (status === 'success' && message.type === 'ai' && id !== 'welcome' && currentAgent) {
        setTimeout(() => {
          loadConversation(activeKey, currentAgent.id);
        }, 1000);
      }

      return {
        key: lId,
        loading: loading,
        role: message.type,
        content: content,
        footer: footer,
      };
    });
  };

  return {
    renderMarkdown,
    retrieverPopover,
    retrieverPopoverContent,
    renderMessageItems,
  };
};
