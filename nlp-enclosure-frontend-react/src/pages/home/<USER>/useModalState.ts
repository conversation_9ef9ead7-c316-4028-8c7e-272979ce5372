import { useState } from 'react';
import { Form } from 'antd';

export interface ModalState {
  // 重命名弹窗状态
  isShowRenameModal: boolean;
  setIsShowRenameModal: (value: boolean) => void;
  
  // 重命名弹窗类型
  renameModalType: 'rename' | 'topChat';
  setRenameModalType: (value: 'rename' | 'topChat') => void;
  
  // Agent表单弹窗状态
  isOpenAgentFormModal: boolean;
  setIsOpenAgentFormModal: (value: boolean) => void;
  
  // 可见性选择器弹窗状态
  isShowVisibleSelectorDialog: boolean;
  setIsShowVisibleSelectorDialog: (value: boolean) => void;
  
  // 可见性选择的Agent ID
  visibleSelectAgentDictId: string;
  setVisibleSelectAgentDictId: (value: string) => void;
  
  // 待重命名的对话ID
  beRenameConversionId: string | null;
  setBeRenameConversionId: (value: string | null) => void;
  
  // 聊天名称表单
  chatNameForm: any;
  
  // 重置所有弹窗状态
  resetModalState: () => void;
}

export const useModalState = (): ModalState => {
  // 重命名弹窗状态
  const [isShowRenameModal, setIsShowRenameModal] = useState(false);
  
  // 重命名弹窗类型
  const [renameModalType, setRenameModalType] = useState<'rename' | 'topChat'>('rename');
  
  // Agent表单弹窗状态
  const [isOpenAgentFormModal, setIsOpenAgentFormModal] = useState(false);
  
  // 可见性选择器弹窗状态
  const [isShowVisibleSelectorDialog, setIsShowVisibleSelectorDialog] = useState(false);
  
  // 可见性选择的Agent ID
  const [visibleSelectAgentDictId, setVisibleSelectAgentDictId] = useState<string>('');
  
  // 待重命名的对话ID
  const [beRenameConversionId, setBeRenameConversionId] = useState<string | null>(null);
  
  // 聊天名称表单
  const [chatNameForm] = Form.useForm<{
    name: string;
  }>();

  // 重置所有弹窗状态
  const resetModalState = () => {
    setIsShowRenameModal(false);
    setRenameModalType('rename');
    setIsOpenAgentFormModal(false);
    setIsShowVisibleSelectorDialog(false);
    setVisibleSelectAgentDictId('');
    setBeRenameConversionId(null);
    chatNameForm.resetFields();
  };

  return {
    isShowRenameModal,
    setIsShowRenameModal,
    renameModalType,
    setRenameModalType,
    isOpenAgentFormModal,
    setIsOpenAgentFormModal,
    isShowVisibleSelectorDialog,
    setIsShowVisibleSelectorDialog,
    visibleSelectAgentDictId,
    setVisibleSelectAgentDictId,
    beRenameConversionId,
    setBeRenameConversionId,
    chatNameForm,
    resetModalState,
  };
};
