import { useState, useRef } from 'react';
import { Avatar } from 'antd';
import { AlignLeftOutlined } from '@ant-design/icons';
import { Conversation } from '@ant-design/x/es/conversations';
import { getChatHistoryList } from '../../../services/chatHistory';
import { getAgentMessage } from '../../../services/difyApi';
import { getBaseUrlWithoutProtocol } from '../../../services/websocketService';
import { AgentDict, getColorPairByBgColor } from '../../../types/agentDict';
import { MessageInfo } from '@ant-design/x/es/use-x-chat';
import { AgentMessage } from '../../../types/streamResponse';

export interface ConversationManager {
  // 对话列表状态
  conversationsItems: Conversation[];
  setConversationsItems: (items: Conversation[]) => void;
  
  // 刷新状态
  isBeRefreshConversations: boolean;
  setIsBeRefreshConversations: (value: boolean) => void;
  isBeRefreshConversationsRef: React.MutableRefObject<boolean>;
  
  // 待加载历史消息的对话ID
  beLoadHistoryMessageConversationId: string;
  setBeLoadHistoryMessageConversationId: (value: string) => void;
  
  // 业务方法
  loadConversation: (conversationId?: string, agentId?: string) => Promise<Conversation[]>;
  loadHistoryMessage: (conversationId: string, currentAgent: AgentDict | null, setMessages: (messages: MessageInfo<AgentMessage>[]) => void, setMessageFeedBackDict: (dict: any) => void, updateDefaultAgentStateByMessage: (message: any) => void) => Promise<void>;
}

export const useConversationManager = (): ConversationManager => {
  // 对话列表状态
  const [conversationsItems, setConversationsItems] = useState<Conversation[]>([]);
  
  // 刷新状态
  const [isBeRefreshConversations, setIsBeRefreshConversations] = useState(false);
  const isBeRefreshConversationsRef = useRef(isBeRefreshConversations);
  
  // 待加载历史消息的对话ID
  const [beLoadHistoryMessageConversationId, setBeLoadHistoryMessageConversationId] = useState('');

  // 更新ref
  isBeRefreshConversationsRef.current = isBeRefreshConversations;

  // 加载对话列表
  const loadConversation = async (conversationId?: string, agentId?: string): Promise<Conversation[]> => {
    if (conversationId && conversationId.startsWith('auto')) {
      return [];
    }

    if (conversationId && agentId && !isBeRefreshConversationsRef.current) {
      return [];
    }

    setIsBeRefreshConversations(false);

    try {
      const conversations = await getChatHistoryList(conversationId, agentId);
      if (conversations && conversations.length > 0) {
        // 转换格式
        const formattedConversations = conversations.map((conversation) => ({
          key: conversation.id,
          label: conversation.name,
          isTop: conversation.isTop,
          conversationId: conversation.conversationId,
          agentDict: conversation.agentDict,
          timestamp: new Date(conversation.createAt).getTime(),
          icon:
            conversation.agentDict.isDefault ?
              (<Avatar size="small" style={{ backgroundColor: 'rgb(243, 244, 246)'}}><AlignLeftOutlined style={{ color: 'rgba(0, 0, 0, 0.88)' }} /></Avatar>)
              :
              (<Avatar size="small" style={{
                backgroundColor: conversation.agentDict.iconColor || '#dfdff8',
                color: getColorPairByBgColor(conversation.agentDict.iconColor).textColor,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center'
              }}
              src={conversation.agentDict.icon && (conversation.agentDict.icon.startsWith('http://') || conversation.agentDict.icon.startsWith('https://'))
                ? conversation.agentDict.icon
                : undefined}
              >
                {conversation.agentDict.icon && !(conversation.agentDict.icon.startsWith('http://') || conversation.agentDict.icon.startsWith('https://')) ? (
                  <span style={{ fontSize: '14px' }}>{conversation.agentDict.icon}</span>
                ) : (
                  !conversation.agentDict.icon && (conversation.agentDict.name ? conversation.agentDict.name[0] : '?')
                )}
              </Avatar>)
        }));

        setConversationsItems(formattedConversations);
        return formattedConversations;
      } else {
        setConversationsItems([]);
        return [];
      }
    } catch (ex) {
      console.error('Error loading conversations:', ex);
    }
    return [];
  };

  // 加载历史消息
  const loadHistoryMessage = async (
    conversationId: string, 
    currentAgent: AgentDict | null, 
    setMessages: (messages: MessageInfo<AgentMessage>[]) => void,
    setMessageFeedBackDict: (dict: any) => void,
    updateDefaultAgentStateByMessage: (message: any) => void
  ): Promise<void> => {
    if (!currentAgent || !conversationId) {
      return;
    }

    const messageRes = await getAgentMessage(currentAgent.id, conversationId);

    if (messageRes.data && messageRes.data.length > 0) {
      setMessageFeedBackDict({});

      updateDefaultAgentStateByMessage(messageRes.data[0]);

      const array: MessageInfo<AgentMessage>[] = [];
      const hostUrl = getBaseUrlWithoutProtocol(true);
      
      messageRes.data.forEach((message: any) => {
        let userFiles = [];
        let aiFiles = [];
        
        if (message.message_files && message.message_files.length > 0) {
          userFiles = message.message_files.filter((file: any) => file.belongs_to === 'user');
          if (userFiles.length > 0) {
            userFiles.forEach((file: any) => {
              file.url = hostUrl + '/dfile' + file.url;
            });
          }

          aiFiles = message.message_files.filter((file: any) => file.belongs_to !== 'user');
          if (aiFiles.length > 0) {
            aiFiles.forEach((file: any) => {
              file.url = hostUrl + '/dfile' + file.url;
            });
          }
        }

        if (message.feedback && message.feedback.rating) {
          setMessageFeedBackDict((prev: any) => ({
            ...prev,
            [message.id]: message.feedback.rating
          }));
        }

        // 替换<think>标签的内容
        let answer = message.answer;
        if (answer.startsWith("<think>")) {
          answer = answer
            .replace(/<think>/g, '<details style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 12px;" open> <summary> 思考过程... </summary>')
            .replace(/<\/think>/g, '</details>');
        }

        array.push(
          {
            id: message.id,
            message: {
              type: 'local',
              content: message.query,
              suggested: false,
              fileList: userFiles
            },
            status: 'local'
          },
          {
            id: message.id,
            message: {
              type: 'ai',
              content: answer,
              suggested: false,
              fileList: aiFiles,
              feedback: message.feedback
            },
            status: 'success'
          }
        );
      });
      setMessages(array);
    }
  };

  return {
    conversationsItems,
    setConversationsItems,
    isBeRefreshConversations,
    setIsBeRefreshConversations,
    isBeRefreshConversationsRef,
    beLoadHistoryMessageConversationId,
    setBeLoadHistoryMessageConversationId,
    loadConversation,
    loadHistoryMessage,
  };
};
