import React from 'react';
import { Button } from 'antd';
import { MenuUnfoldOutlined, MenuFoldOutlined } from '@ant-design/icons';

interface MenuToggleButtonProps {
  collapsed: boolean;
  isMobile: boolean;
  onToggle: () => void;
}

const MenuToggleButton: React.FC<MenuToggleButtonProps> = ({
  collapsed,
  isMobile,
  onToggle
}) => {
  return (
    <Button
      type="text"
      variant="text"
      shape="circle"
      icon={isMobile ? <MenuUnfoldOutlined /> : collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
      onClick={onToggle}
      style={{
        fontSize: '16px',
        marginLeft: 10
      }}
    />
  );
};

export default MenuToggleButton;
