import React from 'react';
import { But<PERSON>, Badge, ConfigProvider } from 'antd';
import { FileAddOutlined } from '@ant-design/icons';

interface FileUploadButtonProps {
  headerOpen: boolean;
  attachedFilesCount: number;
  onToggle: () => void;
}

const FileUploadButton: React.FC<FileUploadButtonProps> = ({
  headerOpen,
  attachedFilesCount,
  onToggle
}) => {
  return (
    <ConfigProvider
      theme={{
        components: {
          Button: {
            defaultColor: 'rgba(0, 0, 0, 0.85)',
            defaultHoverBg: 'rgba(0, 0, 0, 0.1)',
            defaultHoverColor: 'rgba(0, 0, 0, 0.85)',
            defaultHoverBorderColor: 'rgb(217, 217, 217)',
          }
        }
      }}
    >
      <Button 
        icon={<FileAddOutlined />}
        onClick={onToggle}
        style={{
          marginRight: '10px',
        }}
      >
        <Badge dot={attachedFilesCount > 0 && !headerOpen}>
          上传参考文件
        </Badge>
      </Button>
    </ConfigProvider>
  );
};

export default FileUploadButton;
