import React from 'react';
import { Button, ConfigProvider } from 'antd';
import { PlusOutlined } from '@ant-design/icons';

interface CreateAgentButtonProps {
  isCanCreateAgent: boolean;
  onClick: () => void;
}

const CreateAgentButton: React.FC<CreateAgentButtonProps> = ({
  isCanCreateAgent,
  onClick
}) => {
  return (
    <ConfigProvider
      theme={{
        components: {
          Button: {
            defaultColor: 'rgba(0, 0, 0, 0.85)',
            defaultHoverBg: 'rgba(0, 0, 0, 0.1)',
            defaultHoverColor: 'rgba(0, 0, 0, 0.85)',
            defaultHoverBorderColor: 'rgb(217, 217, 217)',
          }
        }
      }}
    >
      <Button
        icon={<PlusOutlined />}
        onClick={onClick}
        disabled={!isCanCreateAgent}
      >
        <span>创建 Agent</span>
      </Button>
    </ConfigProvider>
  );
};

export default CreateAgentButton;
