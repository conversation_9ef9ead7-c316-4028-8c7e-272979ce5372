import React from 'react';
import { Radio, ConfigProvider } from 'antd';
import { ReactComponent as QwenThinkIcon } from '../../img/qwenThink.svg';

interface DeepThinkButtonProps {
  defaultAgentIsOpenThink: boolean;
  isDisableSwitchOpenThink: boolean;
  hasMessages: boolean;
  onToggle: () => void;
}

const DeepThinkButton: React.FC<DeepThinkButtonProps> = ({
  defaultAgentIsOpenThink,
  isDisableSwitchOpenThink,
  hasMessages,
  onToggle
}) => {
  return (
    <ConfigProvider
      theme={{
        components: {
          Radio: {
            buttonColor: 'rgba(0, 0, 0, 0.85)',
            buttonCheckedBg: 'rgba(0, 0, 0, 0.1)',
            colorPrimaryBorder: 'rgb(217, 217, 217)',
            buttonCheckedBgDisabled: '#1677ff',
            buttonCheckedColorDisabled: '#ffffff',
          }
        }
      }}
    >
      <Radio.Group
        buttonStyle={"solid"}
        value={defaultAgentIsOpenThink}
        disabled={isDisableSwitchOpenThink || hasMessages}
        className="think-radio-btn"
      >
        <Radio.Button
          value={true}
          onClick={(event) => {
            event.stopPropagation();
            onToggle();
          }}
          style={{
            marginRight: '10px'
          }}
        >
          <div className="flex justify-center items-center ">
            <QwenThinkIcon className="qwen-think-icon" />
            <span>深度思考</span>
          </div>
        </Radio.Button>
      </Radio.Group>
    </ConfigProvider>
  );
};

export default DeepThinkButton;
