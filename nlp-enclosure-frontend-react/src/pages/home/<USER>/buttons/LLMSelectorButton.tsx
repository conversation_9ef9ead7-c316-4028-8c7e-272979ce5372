import React from 'react';
import { Dropdown, Space } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import { AgentDict } from '../../../../types/agentDict';

interface LLMSelectorButtonProps {
  currentAgent: AgentDict | null;
  llmDefaultSelect: string;
  llmItems: any[];
  onLLMSelect: (info: any) => void;
}

const LLMSelectorButton: React.FC<LLMSelectorButtonProps> = ({
  currentAgent,
  llmDefaultSelect,
  llmItems,
  onLLMSelect
}) => {
  if (!currentAgent?.isDefault) {
    return null;
  }

  return (
    <Dropdown 
      menu={{
        items: llmItems,
        selectable: true,
        defaultSelectedKeys: [llmDefaultSelect],
        onClick: onLLMSelect
      }}
      trigger={['click']}
    >
      <div className="ml-10">
        <Space>
          <span className="text-base">{llmDefaultSelect}</span>
          <DownOutlined/>
        </Space>
      </div>
    </Dropdown>
  );
};

export default LLMSelectorButton;
