import React from 'react';
import { Radio, ConfigProvider } from 'antd';
import { SoundOutlined } from '@ant-design/icons';

interface RealtimePlayButtonProps {
  realtimePlayEnabled: boolean;
  onToggle: () => void;
}

const RealtimePlayButton: React.FC<RealtimePlayButtonProps> = ({
  realtimePlayEnabled,
  onToggle
}) => {
  return (
    <ConfigProvider
      theme={{
        components: {
          Radio: {
            buttonColor: 'rgba(0, 0, 0, 0.85)',
            buttonCheckedBg: 'rgba(0, 0, 0, 0.1)',
            colorPrimaryBorder: 'rgb(217, 217, 217)',
            buttonCheckedBgDisabled: '#1677ff',
            buttonCheckedColorDisabled: '#ffffff',
          }
        }
      }}
    >
      <Radio.Group
        buttonStyle={"solid"}
        value={realtimePlayEnabled}
        className="audio-player-radio-btn"
      >
        <Radio.Button
          value={true}
          onClick={(event) => {
            event.stopPropagation();
            onToggle();
          }}
        >
          <div className="flex justify-center items-center ">
            <SoundOutlined style={{marginRight: 5}} />
            <span>实时播放</span>
          </div>
        </Radio.Button>
      </Radio.Group>
    </ConfigProvider>
  );
};

export default RealtimePlayButton;
