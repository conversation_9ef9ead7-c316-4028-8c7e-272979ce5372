import React from 'react';
import { Bubble } from '@ant-design/x';
import { useMessageRenderer } from '../hooks/useMessageRenderer';
import { bubbleRoles } from '../config/chatConfig';
import { AgentParam } from '../../../types/agentParam';
import { MessageInfo } from '@ant-design/x/es/use-x-chat';
import { AgentMessage } from '../../../types/streamResponse';

interface MessageListProps {
  parsedMessages: any[];
  currentAgentParam: AgentParam | null;
  messageFeedBackDict: { [key: string]: string | null };
  handleFeedback: (messageId: string, rating: string | null) => void;
  handleSuggestionPromptsClick: (prompt: string) => void;
  loadConversation: (activeKey: string, agentId: string) => Promise<any>;
  activeKey: string;
  currentAgent: any;
  realtimePlayEnabled: boolean;
  style?: React.CSSProperties;
  className?: string;
}

const MessageList: React.FC<MessageListProps> = ({
  parsedMessages,
  currentAgentParam,
  messageFeedBackDict,
  handleFeedback,
  handleSuggestionPromptsClick,
  loadConversation,
  activeKey,
  currentAgent,
  realtimePlayEnabled,
  style,
  className
}) => {
  const messageRenderer = useMessageRenderer();

  // 渲染消息项
  const items = messageRenderer.renderMessageItems(
    parsedMessages,
    currentAgentParam,
    messageFeedBackDict,
    handleFeedback,
    loadConversation,
    activeKey,
    currentAgent,
    realtimePlayEnabled
  );

  // 创建带有建议点击处理的roles配置
  const rolesWithSuggestionHandler = {
    ...bubbleRoles,
    suggestion: {
      ...bubbleRoles.suggestion,
      messageRender: (content: any) => 
        bubbleRoles.suggestion.messageRender(content, handleSuggestionPromptsClick)
    }
  };

  return (
    <Bubble.List
      items={items}
      roles={rolesWithSuggestionHandler}
      style={style}
      className={className}
    />
  );
};

export default MessageList;
