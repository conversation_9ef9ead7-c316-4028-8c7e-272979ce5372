import React from 'react';
import { ContentShowState } from '../config/chatConfig';
import ChatInterface from './ChatInterface';
import AgentManager from './AgentManager';
import SuperSonicAgent from './SuperSonicAgent';

interface ContentRendererProps {
  contentShowState: ContentShowState;
  currentSuperSonicAgentId: string | null;
  selectedAgentId: string;
  
  // Chat Interface Props
  chatInterfaceProps?: {
    parsedMessages: any[];
    currentAgentParam: any;
    messageFeedBackDict: { [key: string]: string | null };
    handleFeedback: (messageId: string, rating: string | null) => void;
    handleSuggestionPromptsClick: (prompt: string) => void;
    loadConversation: (activeKey: string, agentId: string) => Promise<any>;
    activeKey: string;
    currentAgent: any;
    realtimePlayEnabled: boolean;
    onSubmit: (message: string) => void;
    onCancel: () => void;
    content: string;
    setContent: (content: string) => void;
    attachedFiles: any[];
    setAttachedFiles: (files: any[]) => void;
    suggestedPrompts: any[];
    currentTaskId: string | null;
    isSpeechRecording: boolean;
    setIsSpeechRecording: (recording: boolean) => void;
  };
  
  // Agent Manager Props
  agentManagerProps?: {
    agentList: any[];
    currentUserAgentList: any[];
    isCanCreateAgent: boolean;
    handleAgentClick: (agent: any) => void;
    handleAgentVisibleClick: (agent: any) => void;
    handleCreateAgentClick: () => void;
    handleDeleteUserAgent: (userAgentId: string) => void;
  };
  
  // SuperSonic Agent Props
  superSonicProps?: {
    agentId: string;
    onBack: () => void;
  };
}

const ContentRenderer: React.FC<ContentRendererProps> = ({
  contentShowState,
  currentSuperSonicAgentId,
  selectedAgentId,
  chatInterfaceProps,
  agentManagerProps,
  superSonicProps
}) => {
  
  const renderContent = () => {
    switch (contentShowState) {
      case 'defaultChat':
      case 'agent':
        return chatInterfaceProps ? (
          <ChatInterface {...chatInterfaceProps} />
        ) : null;
        
      case 'agentManager':
        return agentManagerProps ? (
          <AgentManager {...agentManagerProps} />
        ) : null;
        
      case 'supersonic':
        return superSonicProps && currentSuperSonicAgentId ? (
          <SuperSonicAgent 
            agentId={currentSuperSonicAgentId}
            onBack={superSonicProps.onBack}
          />
        ) : null;
        
      default:
        return chatInterfaceProps ? (
          <ChatInterface {...chatInterfaceProps} />
        ) : null;
    }
  };

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {renderContent()}
    </div>
  );
};

export default ContentRenderer;
