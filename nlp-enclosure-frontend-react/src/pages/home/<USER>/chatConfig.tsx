import React from 'react';
import { GetProp } from 'antd';
import { Bubble, Prompts, ConversationsProps } from '@ant-design/x';
import { PushpinOutlined, EditOutlined, DeleteOutlined, ArrowRightOutlined } from '@ant-design/icons';
import styles from '../index.module.css';

// Bubble.List 的 roles 配置
export const bubbleRoles: GetProp<typeof Bubble.List, 'roles'> = {
  ai: {
    placement: 'start',
    typing: { step: 1, interval: 500 },
    styles: {
      content: {
        backgroundColor: 'rgba(255,255,255)',
        marginLeft: 10,
        marginRight: 10,
        paddingBottom: 0
      },
      footer: {
        width: '100%'
      }
    }
  },
  local: {
    placement: 'end',
    styles: {
      content: {
        backgroundColor: 'rgba(0,0,0,0.04)',
        color: 'rgba(0,0,0,0.85)',
        borderRadius: 12,
        fontSize: 15,
        marginLeft: 10,
        marginRight: 10
      },
      footer: {
        width: '100%'
      }
    },
    shape: 'round',
  },
  suggestion: {
    placement: 'start',
    variant: 'borderless',
    messageRender: (content, handleSuggestionPromptsClick) => (
      <Prompts
        vertical={true}
        items={(content as any as string[]).map((text) => ({
          key: text,
          description: (
            <div>
              <span>{text}</span>
              <span style={{ marginLeft: 10 }}>
                <ArrowRightOutlined />
              </span>
            </div>
          )
        }))}
        onItemClick={(key) => {
          handleSuggestionPromptsClick?.(key.data);
        }}
        style={{
          marginLeft: 20
        }}
        classNames={{
          item: styles.suggestionPrompts
        }}
      />
    )
  }
};

// 对话菜单配置
export const createConversationsMenu = (
  handleConversationMenuClick: (conversationId: string, menuKey: string, conversationName: string, isTop: boolean) => void
): ConversationsProps['menu'] => (conversation) => ({
  items: [
    {
      label: conversation['isTop'] ? '取消固定' : '固定',
      key: 'top',
      icon: <PushpinOutlined />
    },
    {
      label: '重命名',
      key: 'rename',
      icon: <EditOutlined />
    },
    {
      label: '删除',
      key: 'delete',
      icon: <DeleteOutlined />,
      danger: true
    }
  ],
  onClick: ({ key }) => {
    handleConversationMenuClick(
      conversation.conversationId,
      key,
      conversation.label,
      conversation.isTop
    );
  }
});

// 按钮主题配置
export const buttonTheme = {
  components: {
    Button: {
      colorPrimary: '#000',
      algorithm: true,
    },
  },
};

// LLM 选项配置
export const llmOptions = [
  { label: 'Qwen2.5:32B-instruct', value: 'Qwen2.5:32B-instruct' },
  { label: 'Qwen2.5-VL:32B-instruct', value: 'Qwen2.5-VL:32B-instruct' },
  { label: 'DeepSeek-R1:671B', value: 'DeepSeek-R1:671B' }
];

// 默认建议问题
export const defaultSuggestedPrompts = [
  { key: '1', description: '你好，请介绍一下你自己' },
  { key: '2', description: '今天天气怎么样？' },
  { key: '3', description: '帮我写一个简单的Python程序' },
  { key: '4', description: '解释一下什么是人工智能' }
];

// 内容显示状态类型
export type ContentShowState = 'defaultChat' | 'agent' | 'agentManager' | 'supersonic';

// 重命名弹窗类型
export type RenameModalType = 'rename' | 'topChat';
